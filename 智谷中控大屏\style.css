* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

:root {
    --primary-color: #1E90FF;
    --secondary-color: #4169E1;
    --accent-color: #87CEEB;
    --dark-bg: #0a0e1a;
    --card-bg: rgba(6, 15, 30, 0.85);
    --light-text: #e8f4fd;
    --border-color: rgba(30, 144, 255, 0.3);
    --highlight-color: #0066CC;
    --gradient-blue: linear-gradient(135deg, #1E90FF, #0066CC);
    --gradient-secondary: linear-gradient(135deg, #4169E1, #191970);
    --gradient-accent: linear-gradient(135deg, #87CEEB, #4682B4);
    --glow-primary: 0 0 20px rgba(30, 144, 255, 0.5);
    --glow-secondary: 0 0 20px rgba(65, 105, 225, 0.5);
    --glow-accent: 0 0 20px rgba(135, 206, 235, 0.5);
}

body {
    background: linear-gradient(135deg, #0a0e1a 0%, #1a1f35 50%, #0f1419 100%);
    color: var(--light-text);
    height: 100vh;
    overflow: hidden;
    position: relative;
}

/* 添加动态背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(30, 144, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(65, 105, 225, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(135, 206, 235, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 10px;
    position: relative;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 25px;
    background: rgba(6, 15, 30, 0.9);
    border-radius: 12px;
    margin-bottom: 8px;
    border: 2px solid var(--border-color);
    box-shadow: var(--glow-primary);
    position: relative;
    overflow: hidden;
}

/* 添加头部动态边框效果 */
.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    animation: borderFlow 3s linear infinite;
}

@keyframes borderFlow {
    0% { left: -100%; }
    100% { left: 100%; }
}

.dashboard-header h1 {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(30, 144, 255, 0.8);
    letter-spacing: 2px;
}

.time-display {
    font-size: 20px;
    color: var(--primary-color);
    font-weight: 500;
    text-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
}

.dashboard-body {
    flex: 1;
    display: grid;
    grid-template-columns: 450px 1fr 450px;
    grid-template-rows: 1fr 1fr;
    gap: 18px;
    overflow: hidden;
}

.left-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
    grid-row: 1 / 3;
}

.center-panel {
    display: grid;
    grid-template-rows: 2fr 1fr;
    gap: 5px;
    grid-column: 2;
    grid-row: 1 / 3;
}

.right-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
    grid-row: 1 / 3;
}

.center-panel .data-card {
    height: auto;
}

.left-panel .data-card, .right-panel .data-card {
    height: auto;
    min-height: calc(50% - 4px);
}

.data-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    border: 2px solid var(--border-color);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* 卡片发光效果 */
.data-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    padding: 2px;
    background: linear-gradient(45deg, var(--primary-color), transparent, var(--secondary-color));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.data-card:hover::before {
    opacity: 1;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        var(--glow-primary),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
}

.data-card h2 {
    font-size: 18px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--border-color);
    color: var(--primary-color);
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    position: relative;
}

/* 标题装饰效果 */
.data-card h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--gradient-blue);
    box-shadow: 0 0 10px var(--primary-color);
}

.chart-container {
    flex: 1;
    width: 100%;
    height: calc(100% - 45px);
    position: relative;
}

/* 地图容器特殊样式 */
#userDistributionChart {
    height: calc(100% - 30px) !important;
    min-height: 500px;
}

/* 网络流量图表特殊样式 */
#networkFlowChart {
    height: calc(100% - 45px) !important;
    min-height: 320px;
}

/* 行业痛点样式 */
.data-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 10px 0;
    max-height: calc(100% - 80px);
    overflow-y: auto;
}

/* 自定义滚动条 */
.data-list::-webkit-scrollbar {
    width: 6px;
}

.data-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.data-list::-webkit-scrollbar-thumb {
    background: var(--gradient-blue);
    border-radius: 3px;
    box-shadow: 0 0 5px var(--primary-color);
}

.data-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.data-item:hover {
    background: rgba(30, 144, 255, 0.1);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(30, 144, 255, 0.2);
}

.data-title {
    font-weight: 600;
    font-size: 14px;
    color: var(--light-text);
}

.data-progress {
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-blue);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
    transition: width 2s ease-in-out;
    min-width: 35px;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    box-shadow: 0 0 15px rgba(30, 144, 255, 0.6);
}

/* 进度条动画效果 */
.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.data-desc {
    font-size: 12px;
    color: #b8c5d1;
    line-height: 1.4;
}

/* 案例分析样式 */
.case-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

/* 案例导航样式 */
.case-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(0, 212, 255, 0.1);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.case-tabs {
    display: flex;
    gap: 8px;
}

.case-tab {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.case-tab:hover {
    background: rgba(30, 144, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
}

.case-tab.active {
    background: var(--gradient-blue);
    color: white;
    box-shadow: var(--glow-blue);
}

.case-counter {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
}

.case-content {
    display: none;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.case-content.active {
    display: flex;
}

.case-info {
    padding: 15px;
    background: rgba(0, 212, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.case-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.case-time, .case-user, .case-desc {
    font-size: 13px;
    color: #b8c5d1;
    margin-bottom: 4px;
}

.case-response {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    flex: 1;
}

.response-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.response-item:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: translateX(3px);
}

.response-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.response-icon.success {
    background: var(--gradient-green);
    box-shadow: var(--glow-green);
}

.response-icon.process {
    background: var(--gradient-blue);
    box-shadow: var(--glow-blue);
}

.response-icon.alert {
    background: var(--gradient-orange);
    box-shadow: var(--glow-orange);
}

.response-result {
    margin-top: 10px;
    padding: 12px;
    background: rgba(0, 255, 136, 0.1);
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.highlight {
    color: var(--secondary-color);
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.pilot-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    flex: 1;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 10px 0;
    max-height: calc(100% - 120px);
    overflow-y: auto;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: rgba(30, 144, 255, 0.05);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.stat-item:hover {
    background: rgba(30, 144, 255, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(30, 144, 255, 0.3);
    transform: scale(1.05);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 8px;
    text-shadow: 0 0 15px rgba(30, 144, 255, 0.5);
    animation: numberGlow 2s ease-in-out infinite alternate;
}

@keyframes numberGlow {
    0% { text-shadow: 0 0 15px rgba(30, 144, 255, 0.5); }
    100% { text-shadow: 0 0 25px rgba(30, 144, 255, 0.8); }
}

.stat-name {
    font-size: 12px;
    color: #b8c5d1;
    text-align: center;
    line-height: 1.3;
}

.decrease-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.decrease-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    background: rgba(0, 255, 136, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 136, 0.2);
    transition: all 0.3s ease;
}

.decrease-item:hover {
    background: rgba(0, 255, 136, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.decrease-label {
    font-size: 11px;
    color: #b8c5d1;
    margin-bottom: 5px;
    text-align: center;
}

.decrease-value {
    font-size: 16px;
    font-weight: bold;
    color: var(--secondary-color);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .dashboard-body {
        grid-template-columns: 380px 1fr 380px;
    }
}

@media (max-width: 1400px) {
    .dashboard-body {
        grid-template-columns: 320px 1fr 320px;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .stat-value {
        font-size: 18px;
    }

    .stat-name {
        font-size: 10px;
    }
}

@media (max-width: 1200px) {
    .dashboard-body {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }

    .left-panel, .center-panel, .right-panel {
        grid-row: auto;
        grid-column: 1;
    }

    .left-panel .data-card, .right-panel .data-card, .center-panel .data-card {
        height: 400px;
    }

    #userDistributionChart {
        min-height: 350px;
    }
}

@media (max-width: 1000px) {
    .stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }

    .stat-value {
        font-size: 18px;
    }

    .stat-name {
        font-size: 10px;
    }

    .decrease-stats {
        grid-template-columns: repeat(4, 1fr);
    }
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 15px rgba(30, 144, 255, 0.5);
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 0 25px rgba(30, 144, 255, 0.8);
}

/* 数据表格样式 */
.alert-table {
    flex: 1;
    overflow: auto;
    margin-top: 5px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.data-table th {
    background: rgba(30, 144, 255, 0.2);
    color: var(--primary-color);
    padding: 8px 6px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 8px 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--light-text);
    transition: all 0.3s ease;
}

.data-table tr:hover {
    background: rgba(30, 144, 255, 0.1);
    transform: scale(1.02);
}

.data-table tr:hover td {
    color: var(--primary-color);
    text-shadow: 0 0 5px rgba(30, 144, 255, 0.3);
}

/* 统计网格样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.stat-box {
    background: rgba(30, 144, 255, 0.1);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 144, 255, 0.3), transparent);
    animation: statBoxShine 3s ease-in-out infinite;
}

@keyframes statBoxShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.stat-box:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(30, 144, 255, 0.4);
    transform: scale(1.05);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 8px;
    text-shadow: 0 0 15px rgba(30, 144, 255, 0.5);
}

.stat-label {
    font-size: 11px;
    color: #b8c5d1;
    line-height: 1.2;
}

/* 资源列表样式 */
.resource-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 5px 0;
    max-height: calc(100% - 60px);
    overflow-y: auto;
}

.resource-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--secondary-color);
    transition: all 0.3s ease;
    position: relative;
}

.resource-item:hover {
    background: rgba(65, 105, 225, 0.1);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(65, 105, 225, 0.2);
}

.resource-name {
    font-size: 13px;
    color: var(--light-text);
    flex: 1;
    margin-right: 15px;
}

.resource-value {
    font-size: 16px;
    font-weight: bold;
    color: var(--secondary-color);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    min-width: 60px;
    text-align: right;
}

/* 自定义滚动条样式 */
.alert-table::-webkit-scrollbar,
.resource-list::-webkit-scrollbar {
    width: 6px;
}

.alert-table::-webkit-scrollbar-track,
.resource-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.alert-table::-webkit-scrollbar-thumb,
.resource-list::-webkit-scrollbar-thumb {
    background: var(--gradient-blue);
    border-radius: 3px;
    box-shadow: 0 0 5px var(--primary-color);
}

/* 响应式调整 */
@media (max-width: 1400px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 10px;
    }

    .data-table {
        font-size: 11px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 1000px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 9px;
    }

    .resource-item {
        padding: 12px;
    }

    .resource-name {
        font-size: 12px;
    }

    .resource-value {
        font-size: 14px;
    }
}
