// 在线用户数图表 - 更新为AIGC市场规模和用户增长
function initOnlineUsersChart() {
    const chartDom = document.getElementById('onlineUsersChart');
    const myChart = echarts.init(chartDom);

    const option = {
        grid: {
            top: 50,
            right: 30,
            bottom: 40,
            left: 50
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 15, 30, 0.9)',
            borderColor: '#1E90FF',
            textStyle: {
                color: '#e8f4fd'
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                });
                return result;
            }
        },
        legend: {
            data: ['市场规模', '用户增长', '技术成熟度'],
            textStyle: {
                color: '#e8f4fd',
                fontSize: 10
            },
            top: 5,
            itemWidth: 12,
            itemHeight: 8
        },
        xAxis: {
            type: 'category',
            data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#87CEEB',
                fontSize: 9
            },
            splitLine: {
                show: false
            }
        },
        yAxis: {
            type: 'value',
            show: false,
            min: 0,
            max: 100
        },
        series: [
            {
                name: '市场规模',
                type: 'line',
                data: [20, 25, 30, 35, 45, 50, 55, 45, 30, 70, 65, 55],
                lineStyle: {
                    color: '#1E90FF',
                    width: 2
                },
                itemStyle: {
                    color: '#1E90FF',
                    borderWidth: 0
                },
                symbol: 'circle',
                symbolSize: 4,
                smooth: true,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(30, 144, 255, 0.3)' },
                        { offset: 1, color: 'rgba(30, 144, 255, 0.05)' }
                    ])
                }
            },
            {
                name: '用户增长',
                type: 'line',
                data: [15, 20, 28, 32, 40, 48, 52, 50, 62, 57, 53, 78],
                lineStyle: {
                    color: '#4169E1',
                    width: 2
                },
                itemStyle: {
                    color: '#4169E1',
                    borderWidth: 0
                },
                symbol: 'circle',
                symbolSize: 4,
                smooth: true,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(65, 105, 225, 0.2)' },
                        { offset: 1, color: 'rgba(65, 105, 225, 0.05)' }
                    ])
                }
            },
            {
                name: '技术成熟度',
                type: 'line',
                data: [10, 18, 25, 30, 38, 45, 50, 55, 60, 65, 53, 63],
                lineStyle: {
                    color: '#87CEEB',
                    width: 2
                },
                itemStyle: {
                    color: '#87CEEB',
                    borderWidth: 0
                },
                symbol: 'circle',
                symbolSize: 4,
                smooth: true,
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(135, 206, 235, 0.2)' },
                        { offset: 1, color: 'rgba(135, 206, 235, 0.05)' }
                    ])
                }
            }
        ]
    };

    myChart.setOption(option);
    window.addEventListener('resize', () => myChart.resize());
}

// 网络流量图表 - 更新为反诈系统技术性能指标
function initNetworkFlowChart() {
    const chartDom = document.getElementById('networkFlowChart');
    const myChart = echarts.init(chartDom);

    const option = {
        grid: [
            {
                top: 40,
                right: 30,
                bottom: '55%',
                left: 90
            },
            {
                top: '60%',
                right: '8%',
                bottom: 25,
                left: '8%'
            }
        ],
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 15, 30, 0.9)',
            borderColor: '#1E90FF',
            textStyle: {
                color: '#e8f4fd'
            }
        },
        legend: {
            data: ['技术性能指标'],
            textStyle: {
                color: '#e8f4fd',
                fontSize: 11
            },
            top: 10
        },
        xAxis: [
            {
                gridIndex: 0,
                type: 'category',
                data: ['OCR识别', '知识检索', '答案生成', '语音合成', '安全防护', '数字人交互'],
                axisLine: {
                    lineStyle: {
                        color: '#1E90FF'
                    }
                },
                axisLabel: {
                    color: '#e8f4fd',
                    fontSize: 9,
                    rotate: 15,
                    margin: 8
                }
            }
        ],
        yAxis: [
            {
                gridIndex: 0,
                type: 'value',
                min: 85,
                max: 100,
                axisLine: {
                    lineStyle: {
                        color: '#1E90FF'
                    }
                },
                axisLabel: {
                    color: '#e8f4fd',
                    fontSize: 10,
                    formatter: '{value}%'
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(30, 144, 255, 0.2)'
                    }
                }
            }
        ],
        series: [
            {
                name: '技术性能指标',
                type: 'bar',
                xAxisIndex: 0,
                yAxisIndex: 0,
                data: [98, 95, 93, 90, 99.7, 90],
                barWidth: '50%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#1E90FF' },
                        { offset: 1, color: 'rgba(30, 144, 255, 0.6)' }
                    ]),
                    borderRadius: [4, 4, 0, 0],
                    shadowColor: 'rgba(30, 144, 255, 0.4)',
                    shadowBlur: 8,
                    shadowOffsetY: 2
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#e8f4fd',
                    fontSize: 10,
                    formatter: '{c}%',
                    offset: [0, -8],
                    fontWeight: 'bold'
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4169E1' },
                            { offset: 1, color: 'rgba(65, 105, 225, 0.8)' }
                        ]),
                        shadowColor: 'rgba(30, 144, 255, 0.6)',
                        shadowBlur: 12
                    }
                }
            },
            {
                name: '系统响应时间分布',
                type: 'pie',
                radius: ['25%', '55%'],
                center: ['50%', '80%'],
                data: [
                    { value: 0.5, name: '检索响应≤0.5秒' },
                    { value: 2, name: '预警响应≤2秒' },
                    { value: 97.5, name: '其他处理时间' }
                ],
                itemStyle: {
                    borderRadius: 5,
                    borderColor: '#0a0e1a',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    formatter: '{b}\n{d}%',
                    color: '#e8f4fd',
                    fontSize: 8,
                    position: 'inside'
                },
                labelLine: {
                    show: false
                },
                color: ['#1E90FF', '#4169E1', '#87CEEB']
            }
        ]
    };

    myChart.setOption(option);
    window.addEventListener('resize', () => myChart.resize());
}

// 用户分布地图 - 更新为试点城市反诈成果
function initUserDistributionChart() {
    const chartDom = document.getElementById('userDistributionChart');
    const myChart = echarts.init(chartDom);

    // 先加载中国地图数据
    fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json')
        .then(response => response.json())
        .then(chinaJson => {
            // 注册地图
            echarts.registerMap('china', chinaJson);

            // 试点城市坐标数据
            const geoCoordMap = {
                '广东': [113.23, 23.16],
                '浙江': [120.19, 30.26],
                '北京': [116.46, 39.92],
                '上海': [121.48, 31.22],
                '河北': [114.48, 38.03],
                '山东': [117, 36.65],
                '江苏': [118.78, 32.04],
                '四川': [104.06, 30.67],
                '河南': [113.65, 34.76],
                '湖北': [114.31, 30.52],
                '湖南': [113, 28.21],
                '安徽': [117.27, 31.86],
                '福建': [119.3, 26.08],
                '江西': [115.89, 28.68],
                '重庆': [106.54, 29.59],
                '天津': [117.2, 39.13],
                '辽宁': [123.38, 41.8],
                '吉林': [125.35, 43.88],
                '黑龙江': [126.63, 45.75],
                '内蒙古': [111.65, 40.82],
                '新疆': [87.68, 43.77],
                '西藏': [91.11, 29.97],
                '青海': [101.74, 36.56],
                '甘肃': [103.73, 36.03],
                '宁夏': [106.27, 38.47],
                '陕西': [108.95, 34.27],
                '山西': [112.53, 37.87],
                '云南': [102.73, 25.04],
                '贵州': [106.71, 26.57],
                '广西': [108.33, 22.84],
                '海南': [110.35, 20.02]
            };

            // 反诈成果数据（以拦截资金万元为单位）
            const data = [
                { name: '广东', value: 20 }, // 清远试点拦截20万元
                { name: '浙江', value: 15 }, // 计划试点
                { name: '北京', value: 12 },
                { name: '上海', value: 10 },
                { name: '河北', value: 8 },
                { name: '山东', value: 7 },
                { name: '江苏', value: 9 },
                { name: '四川', value: 6 },
                { name: '河南', value: 5 },
                { name: '湖北', value: 4 },
                { name: '湖南', value: 4 },
                { name: '安徽', value: 3 },
                { name: '福建', value: 5 },
                { name: '江西', value: 3 },
                { name: '重庆', value: 4 },
                { name: '天津', value: 3 },
                { name: '辽宁', value: 2 },
                { name: '吉林', value: 2 },
                { name: '黑龙江', value: 2 },
                { name: '内蒙古', value: 1 },
                { name: '新疆', value: 1 },
                { name: '西藏', value: 0.5 },
                { name: '青海', value: 0.5 },
                { name: '甘肃', value: 1 },
                { name: '宁夏', value: 1 },
                { name: '陕西', value: 2 },
                { name: '山西', value: 2 },
                { name: '云南', value: 3 },
                { name: '贵州', value: 2 },
                { name: '广西', value: 3 },
                { name: '海南', value: 1 }
            ];

            const convertData = function (data) {
                const res = [];
                for (let i = 0; i < data.length; i++) {
                    const geoCoord = geoCoordMap[data[i].name];
                    if (geoCoord) {
                        res.push({
                            name: data[i].name,
                            value: geoCoord.concat(data[i].value)
                        });
                    }
                }
                return res;
            };

            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: '反诈资金拦截分布(万元)',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#e8f4fd',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(6, 15, 30, 0.9)',
                    borderColor: '#1E90FF',
                    textStyle: {
                        color: '#e8f4fd'
                    },
                    formatter: function(params) {
                        if (params.seriesName === '反诈成果') {
                            return params.name + '<br/>拦截资金: ' + params.value[2] + '万元';
                        }
                        return params.name + '<br/>重点试点地区';
                    }
                },
                geo: {
                    map: 'china',
                    roam: false,
                    zoom: 1.8,
                    center: [105, 35],
                    itemStyle: {
                        areaColor: 'rgba(30, 144, 255, 0.08)',
                        borderColor: '#1E90FF',
                        borderWidth: 1.5
                    },
                    emphasis: {
                        itemStyle: {
                            areaColor: 'rgba(30, 144, 255, 0.2)'
                        }
                    }
                },
                series: [
                    {
                        name: '反诈成果',
                        type: 'scatter',
                        coordinateSystem: 'geo',
                        data: convertData(data),
                        symbolSize: function (val) {
                            return Math.max(val[2] * 2, 8);
                        },
                        label: {
                            show: false
                        },
                        itemStyle: {
                            color: '#4169E1',
                            shadowBlur: 15,
                            shadowColor: 'rgba(65, 105, 225, 1)',
                            borderColor: '#ffffff',
                            borderWidth: 1
                        },
                        emphasis: {
                            scale: true,
                            label: {
                                show: true,
                                formatter: '{b}: {c}万元',
                                position: 'top',
                                color: '#e8f4fd',
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    },
                    {
                        name: '重点试点地区',
                        type: 'effectScatter',
                        coordinateSystem: 'geo',
                        data: convertData([
                            { name: '广东', value: 20 }, // 清远试点
                            { name: '浙江', value: 15 }, // 计划试点
                            { name: '北京', value: 12 },
                            { name: '上海', value: 10 }
                        ]),
                        symbolSize: function (val) {
                            return Math.max(val[2] * 1.5, 15);
                        },
                        showEffectOn: 'render',
                        rippleEffect: {
                            brushType: 'stroke'
                        },
                        hoverAnimation: true,
                        label: {
                            show: true,
                            formatter: '{b}',
                            position: 'right',
                            color: '#4169E1',
                            fontSize: 11,
                            fontWeight: 'bold'
                        },
                        itemStyle: {
                            color: '#87CEEB',
                            shadowBlur: 20,
                            shadowColor: 'rgba(135, 206, 235, 0.8)'
                        },
                        zlevel: 1
                    }
                ]
            };

            myChart.setOption(option);
            window.addEventListener('resize', () => myChart.resize());
        })
        .catch(error => {
            console.error('地图数据加载失败:', error);
            // 如果地图数据加载失败，显示一个简单的散点图
            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: '反诈试点分布',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#e8f4fd',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(6, 15, 30, 0.9)',
                    borderColor: '#1E90FF',
                    textStyle: {
                        color: '#e8f4fd'
                    }
                },
                xAxis: {
                    type: 'value',
                    show: false
                },
                yAxis: {
                    type: 'value',
                    show: false
                },
                series: [{
                    type: 'scatter',
                    data: [[50, 50, 20], [30, 70, 15], [70, 30, 12], [20, 80, 10], [80, 20, 8]],
                    symbolSize: function (data) {
                        return data[2] * 2;
                    },
                    itemStyle: {
                        color: '#4169E1',
                        shadowBlur: 10,
                        shadowColor: 'rgba(65, 105, 225, 0.8)'
                    }
                }]
            };
            myChart.setOption(option);
        });
}

function initErrorRequestsChart() {
    const chartDom = document.getElementById('errorRequestsChart');
    const myChart = echarts.init(chartDom);

    const option = {
        grid: {
            top: 30,
            right: 35,
            bottom: 60, // 增加底部空间
            left: 90
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(6, 15, 30, 0.9)',
            borderColor: '#1E90FF',
            textStyle: {
                color: '#e8f4fd'
            }
        },
        legend: {
            data: ['系统可用性(%)', '平均响应时间(秒)'],
            textStyle: {
                color: '#e8f4fd',
                fontSize: 10
            },
            bottom: 0 // 图例放到底部
        },
        xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月'],
            axisLine: {
                lineStyle: {
                    color: '#1E90FF'
                }
            },
            axisLabel: {
                color: '#e8f4fd',
                fontSize: 10
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '可用性(%)',
                min: 99,
                max: 100,
                axisLine: {
                    lineStyle: {
                        color: '#1E90FF'
                    }
                },
                axisLabel: {
                    color: '#e8f4fd',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(30, 144, 255, 0.2)'
                    }
                }
            },
            {
                type: 'value',
                name: '响应时间(秒)',
                max: 1,
                axisLine: {
                    lineStyle: {
                        color: '#4169E1'
                    }
                },
                axisLabel: {
                    color: '#e8f4fd',
                    fontSize: 10
                }
            }
        ],
        series: [
            {
                name: '系统可用性(%)',
                type: 'line',
                yAxisIndex: 0,
                data: [99.9, 99.95, 99.98, 99.99, 99.99, 99.99],
                lineStyle: {
                    color: '#4169E1',
                    width: 3
                },
                itemStyle: {
                    color: '#4169E1',
                    borderColor: '#ffffff',
                    borderWidth: 2
                },
                smooth: true,
                label: {
                    show: true,
                    position: 'top',
                    color: '#4169E1',
                    fontSize: 10,
                    formatter: function(params) {
                        // 只显示最后一个点
                        return params.dataIndex === 5 ? params.value + '%' : '';
                    }
                }
            },
            {
                name: '平均响应时间(秒)',
                type: 'line',
                yAxisIndex: 1,
                data: [0.8, 0.7, 0.6, 0.5, 0.5, 0.5],
                lineStyle: {
                    color: '#87CEEB',
                    width: 3
                },
                itemStyle: {
                    color: '#87CEEB',
                    borderColor: '#ffffff',
                    borderWidth: 2
                },
                smooth: true,
                label: {
                    show: true,
                    position: 'bottom',
                    color: '#87CEEB',
                    fontSize: 10,
                    formatter: function(params) {
                        // 只显示最后一个点
                        return params.dataIndex === 5 ? params.value + 's' : '';
                    }
                }
            }
        ]
    };

    myChart.setOption(option);
    window.addEventListener('resize', () => myChart.resize());
}
// 数字动画效果
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(element => {
        const finalValue = parseInt(element.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;

        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            element.textContent = Math.floor(currentValue);
        }, 50);
    });

    const resourceValues = document.querySelectorAll('.resource-value');
    resourceValues.forEach(element => {
        const finalValue = parseInt(element.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;

        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            element.textContent = Math.floor(currentValue);
        }, 50);
    });
}

// 表格行闪烁效果
function initTableEffects() {
    const tableRows = document.querySelectorAll('.data-table tbody tr');

    setInterval(() => {
        const randomRow = tableRows[Math.floor(Math.random() * tableRows.length)];
        randomRow.style.background = 'rgba(30, 144, 255, 0.3)';
        randomRow.style.transform = 'scale(1.02)';

        setTimeout(() => {
            randomRow.style.background = '';
            randomRow.style.transform = '';
        }, 1000);
    }, 3000);
}

// 时间显示功能
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// 初始化所有图表和效果
function initDashboard() {
    // 等待DOM加载完成
    setTimeout(() => {
        initOnlineUsersChart();
        initNetworkFlowChart();
        initUserDistributionChart();
        initErrorRequestsChart();
        animateNumbers();
        initTableEffects();
        updateTime();
        // 每秒更新时间
        setInterval(updateTime, 1000);
    }, 100);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initDashboard);

// 窗口大小改变时重新调整图表
window.addEventListener('resize', () => {
    setTimeout(() => {
        const charts = document.querySelectorAll('[id$="Chart"]');
        charts.forEach(chartDom => {
            const chart = echarts.getInstanceByDom(chartDom);
            if (chart) {
                chart.resize();
            }
        });
    }, 100);
});
