# 智谷AI反诈中控大屏系统

## 项目简介

智谷AI反诈中控大屏系统是一个专业的数据可视化平台，专注于反诈骗数据的实时监控和分析展示。系统通过现代化的Web技术栈，为反诈骗工作提供直观、实时的数据支持和决策依据。

## 功能特性

### 🎯 核心功能
- **实时数据监控**：8项关键指标实时更新，包括预警劝阻、拦截统计、资金保护等
- **热点事件分析**：追踪8类主要诈骗事件的热度和关注度变化
- **地理分布展示**：全国各省份事件讨论IP分布的可视化地图
- **典型案例追踪**：4个真实案例的详细分析和处理流程展示
- **试点成果展示**：多维度展示反诈系统的实际效果

### 📊 数据可视化
- **交互式图表**：基于ECharts的丰富图表类型
- **动态效果**：数据递增动画、进度条填充、图表切换等
- **响应式设计**：适配不同屏幕尺寸的显示设备
- **实时更新**：时间显示和数据的实时刷新

### 🎨 用户体验
- **现代化UI**：深色主题配色，符合大屏显示需求
- **流畅动画**：卡片悬停效果、案例自动轮播等
- **直观展示**：清晰的数据层次和视觉引导

## 技术栈

- **前端框架**：原生HTML5 + CSS3 + JavaScript
- **图表库**：ECharts 5.4.3
- **地图数据**：阿里云DataV地理数据服务
- **样式预处理**：CSS3 Grid + Flexbox布局
- **动画效果**：CSS3 Transitions + JavaScript动画

## 项目结构

```
智谷中控大屏/
├── index.html          # 主页面文件
├── style.css           # 样式文件
├── script.js           # 脚本文件
├── images/             # 图片资源目录
│   ├── logo.jpg        # 系统Logo
│   └── background.png  # 背景图片
└── README.md           # 项目说明文档
```

## 安装与运行

### 环境要求
- 现代浏览器（Chrome 80+、Firefox 75+、Safari 13+、Edge 80+）
- 本地Web服务器（推荐）或直接打开HTML文件

### 快速开始

1. **克隆或下载项目**
   ```bash
   git clone [项目地址]
   cd 智谷中控大屏
   ```

2. **启动本地服务器**（推荐）
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用PHP
   php -S localhost:8000
   ```

3. **访问系统**
   打开浏览器访问：`http://localhost:8000`

### 直接运行
也可以直接双击`index.html`文件在浏览器中打开，但可能会遇到跨域问题导致地图数据加载失败。

## 功能模块详解

### 左侧面板
- **热点事件热度分析**：展示8类诈骗事件的热度值和关注度趋势
- **大模型事件分析**：8个关键指标的进度条展示，包括信息真实性、用户体验等

### 中间面板
- **事件讨论IP分布**：中国地图展示各省份的事件讨论热度分布
- **典型案例追踪**：4个真实案例的轮播展示，包含处理流程和效果

### 右侧面板
- **试点成果监测**：柱状图展示预警指令、拦截统计等核心数据
- **实时监测数据**：8项实时指标和4项效果对比数据

## 数据说明

### 实时监测指标
- 预警劝阻指令：4.2万次
- 拦截涉诈电话：1000次
- 拦截涉诈短信：29.6万条
- 资金拦截金额：20万元
- 风险网站识别：856个
- AI智能分析：1.8万次
- 用户举报处理：3245件
- 预防成功率：98.5%

### 效果对比
- 诈骗案件数量：↓30%
- 损失金额：↓30%
- 响应时间：↓65%
- 误报率：↓80%

## 自定义配置

### 修改数据
在`script.js`文件中可以修改各种数据：
- 热点事件数据：`initMarketChart()`函数中的data数组
- 地图分布数据：`initIPDistributionChart()`函数中的mapData数组
- 实时统计数据：HTML中的对应元素

### 样式调整
在`style.css`文件中可以调整：
- 颜色主题：修改CSS变量
- 布局比例：调整Grid和Flexbox属性
- 动画效果：修改transition和animation属性

### 添加新功能
- 在HTML中添加新的数据卡片
- 在CSS中添加对应样式
- 在JavaScript中添加图表初始化函数

## 浏览器兼容性

| 浏览器 | 最低版本 | 说明 |
|--------|----------|------|
| Chrome | 80+ | 完全支持 |
| Firefox | 75+ | 完全支持 |
| Safari | 13+ | 完全支持 |
| Edge | 80+ | 完全支持 |
| IE | 不支持 | 不支持ES6语法 |

## 性能优化

- 使用CDN加载ECharts库，提高加载速度
- 图表懒加载和响应式调整
- CSS3硬件加速动画
- 合理的数据更新频率

## 故障排除

### 常见问题

1. **地图不显示**
   - 检查网络连接，确保能访问阿里云DataV服务
   - 系统会自动降级为柱状图显示

2. **图表显示异常**
   - 确保ECharts库正确加载
   - 检查浏览器控制台是否有JavaScript错误

3. **样式错乱**
   - 确保CSS文件正确加载
   - 检查浏览器是否支持CSS Grid和Flexbox

## 更新日志

### v1.0.0 (2024-12)
- 初始版本发布
- 完整的数据可视化功能
- 响应式布局设计
- 典型案例轮播功能

## 贡献指南

欢迎提交Issue和Pull Request来改进项目：
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues：[GitHub Issues链接]
- 邮箱：[联系邮箱]

---

**智谷AI反诈中控大屏系统** - 让数据可视化助力反诈骗工作 