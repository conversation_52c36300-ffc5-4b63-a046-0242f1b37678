# 中控大屏热点事件可视化系统需求文档

## 项目背景

* 本项目旨在为指挥中心大屏提供实时可视化展示功能，用于监测系统自动识别的网络舆情或诈骗相关热点事件。通过图表和地图直观展示事件的热度和讨论者地域分布，帮助决策者及时掌握网络热点动态与区域关注情况。
* 随着互联网舆情和网络诈骗事件频发，快速准确地呈现热点事件信息和讨论热度成为必要功能。本可视化系统无需人工干预，通过自动化机制获取热点话题及相关数据，实现监测信息的集中呈现。

## 功能需求

* **热点事件列表**：按照关注度（如提及次数或热度评分）降序排列，展示每个事件的标题和对应的关注度数值。列表应突出显示最热门的几个事件，能够一目了然地看到各事件排名和热度值。
* **事件详情信息**：在事件列表中，每个事件项需展示事件标题和关键指标；可附加简短描述（可选）。关注度指标包括提及次数、热度评分或其他综合评分，用于量化事件的热度。
* **IP省份分布**：对每个事件采集的IP地址进行归属地统计，以省级区域为单位统计讨论人数（或IP数量）。在展示中需要呈现每个事件相关的IP分布情况，帮助判断各省对该事件的关注度。
* **中国地图展示**：在可视化页面中添加中国省级地图组件，用于标注各省份的讨论热度分布。地图可显示整体热度分布，也可根据选定事件动态切换显示该事件在各省的讨论热度。
* **趋势图表（可选）**：可选使用折线图或柱状图显示热点事件的关注度随时间变化趋势，如过去数小时或数天的提及次数变化。用于观察事件热度的演变过程。
* **全屏适配**：页面设计为全屏展示模式，适配1080p和4K分辨率的大屏幕，支持横向并排多个可视化组件（例如左侧为事件列表，右侧为地图，再配合趋势图）。页面布局应充分利用大屏宽度，将各组件横向排列，以保证信息同时可见。
* **实时无交互**：本页面为纯展示页面，无需实现用户交互功能（如点击跳转等）。数据应能实时刷新（或定时更新），确保显示内容为最新的监测结果。

## 数据需求

* **热点事件数据**：由系统自动识别得出的热点事件列表，数据结构包括事件ID、标题、关注度指标（如提及次数、热度评分）、时间序列数据等。每条数据至少包含如下字段：`title`（事件标题）、`mentionCount`（提及次数）、`score`（热度评分）、`timestamps`（时间序列列表），以及关联的IP统计信息。
* **IP归属地数据**：针对每个热点事件采集到的讨论者IP地址，经归属地转换后按省份汇总统计，数据结构如：`{ "北京": 120, "上海": 85, ... }`。该数据用于地图展示各省讨论热度。支持事件维度数据和总体维度数据（即所有事件合并统计）。
* **时间序列数据**：用于绘制关注度趋势图的时间序列数据，如每小时/每天的提及次数或热度值。结构示例：`[{ time: "2025-05-20T10:00:00", value: 150 }, ...]`。
* **数据接口占位**：在前端页面中预留数据变量和接口入口，例如在`<script>`标签中定义`var eventData = []; var ipData = {};`等结构体，供后端动态注入。数据更新方式可采用拉取后端API或WebSocket实时推送。
* **数据格式要求**：所有数据字段需命名规范、格式统一，易于前端解析。注意时间字段格式（如ISO8601），省份名称与地图组件的对应关系（一致的标准中文名）。

## 展示方式

* **ECharts可视化**：核心采用ECharts进行图表渲染，包括列表图表、地图和折线/柱状图。ECharts支持灵活的主题和样式配置，适合大屏显示。
* **热点列表组件**：可使用ECharts柱状图或自定义组件实现事件列表，列出前N个热点事件及其热度值。列表风格简洁，文字清晰可读，可考虑在对应柱状图旁展示数值标签。
* **中国地图组件**：采用ECharts的中国地图（省级分布）。每个省的颜色或热力值反映该省IP数量或热度，颜色可采用渐变方案（冷色到暖色）。支持地图放大、tooltip显示省份名和数值（即使无交互，也需显示文本标签）。
* **趋势图表**：使用ECharts折线图或柱状图展示关注度随时间变化。横轴为时间（如小时刻度），纵轴为关注度指标值。图表要清晰标注时间点和数值，可用不同颜色和图形区分多个事件的趋势（如果需要对比）。
* **布局和风格**：整体布局为深色主题大屏风格，背景颜色深沉（如深蓝或黑灰），图表采用鲜明明亮的配色（例如蓝/橙/绿等高对比色）。文字和数值使用高对比度色彩（白色、浅色系），字号偏大，保证远距离易读。组件之间留有足够间距，界面不显拥挤。
* **分辨率适配**：设计时需考虑在1920×1080和3840×2160分辨率下均能正常显示。可使用百分比布局或视口单位（vw/vh）确保元素随屏幕缩放，或根据分辨率设置不同样式。关键是内容在大屏上不失真、不超出边界。

## 技术要求

* **技术栈**：前端基于HTML5、CSS3和JavaScript，图表使用ECharts库。可根据项目需要引入Vue.js或React等框架辅助开发，但需控制整体页面体积，保证大屏流畅运行。
* **全屏显示**：页面默认全屏（可使用`<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">`等配置），并去除滚动条，确保内容占满屏幕。采用CSS设置`body, html { width: 100%; height: 100%; margin:0; overflow:hidden; }`，ECharts容器自动填充布局区域。
* **无交互设计**：前端逻辑中不实现点击、缩放等交互功能，页面仅定时刷新或更新数据。地图和图表的tooltip提示框可设置为常显文本或仅作为展示，不响应用户操作。
* **接口预留**：代码中留出与后端数据对接的接口点，如在JavaScript中定义`fetchData()`函数或`data`变量，前端可通过替换该部分获取真实数据。接口调用可用AJAX/Fetch定时拉取，也可用WebSocket订阅数据更新。
* **性能优化**：针对大屏长时间展示场景，应优化图表绘制性能。例如：对于地图着色使用合适的渲染模式（Canvas或WebGL）、控件少量使用动画效果、数据量大时考虑简化节点数。避免加载过大图片或复杂DOM。
* **兼容性**：建议在企业内部浏览器环境下运行，兼容Chrome、Edge等主流浏览器的最新版本。页面和脚本应稳定运行于PC端和大屏（整机）上，保证离线或内网环境下无异常。
* **安全性**：若数据从后端实时推送，应注意防止跨站脚本攻击（XSS）等安全问题。前端不处理敏感用户输入，仅接收内部可信数据。

## 开发建议

* **组件化开发**：按功能划分模块，如将事件列表、地图、趋势图分别封装为组件（可用ECharts实例或自定义模块）。确保各组件互相独立，便于维护和布局调整。
* **可复用代码**：编写可复用的配置模板，例如地图默认配置、统一的主题调色板、通用的图表配置选项等，减少重复代码。
* **占位数据**：开发阶段可使用模拟数据填充页面，提前验证布局和效果。例如，在`<script>`中定义`var mockHotEvents = [ … ]; var mockIpStats = { … };`，确保样式和逻辑正确。后续只需替换为真实数据接口。
* **布局调试**：利用开发者工具模拟不同分辨率屏幕效果，确保在1080p和4K下文字大小、图表元素合适。注意调整各组件的宽高比例，保证横向排列时信息密度合理。
* **主题设计**：整体风格可参照专业可视化大屏UI，选用深色背景和亮色配色。各组件标题使用一致字体和字号，颜色搭配要有连贯性（例如全部采用公司VI色或行业配色）。
* **文档记录**：编写详细的接口文档和配置说明，包括数据字段含义、图表配置说明等，方便后端对接和后续迭代。版本管理要跟踪需求变动和数据格式调整。
* **测试验证**：在实际大屏上做演示测试，检查图表渲染是否正确、数据刷新是否稳定、页面布局是否适配。确保无抖动、无重叠、无显著性能问题。

## 可扩展性建议

* **多事件筛选**：未来可增加对事件列表的筛选、搜索或分类功能，例如按照事件类型（舆情/诈骗）、时间范围等筛选热度列表，方便用户聚焦特定事件。
* **地图交互扩展**：当前为纯展示，可考虑后续版本增加点击省份查看详情或联动其他图表的功能（例如点击某省高亮显示该省相关事件数据）。
* **图表丰富**：可根据需求扩展更多图表类型，如饼图显示不同类别事件占比、雷达图展示不同维度评分等。也可添加仪表盘等组件显示整体指标。
* **响应式布局**：如果后续需要在不同尺寸屏幕（如竖屏或小屏环境）使用，页面应增加响应式布局支持，使组件能重新排列适配。
* **后端智能分析**：前端可预留与后端算法模型的接口，例如对事件热度进行预测，未来可将预测结果显示在趋势图中，提升洞察力。
* **国际化支持**：如需面向不同语言用户，可将文案抽离，通过多语言资源文件支持中/英文切换。
* **监控和告警**：可扩展增加系统健康监控模块（例如CPU使用率、网络状态等），在大屏一角显示系统状态；或对突发高热度事件自动预警并突出标注。

以上内容构成完整的中控大屏热点事件可视化系统需求文档。文档结构清晰规范，包含了项目背景、功能和数据需求、展示方式、技术细节、开发及扩展建议等关键模块，为后续产品设计和开发提供了详细指导。